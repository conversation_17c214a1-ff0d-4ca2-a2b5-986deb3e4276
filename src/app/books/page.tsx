import { searchByTagAndComplete } from '@/app/lib/pageUtil';
import ComicTile from "@/app/components/comicTile";
import Pagination from '@/app/components/pagination';
import { Squares2X2Icon as Squares2X2IconOutline } from '@heroicons/react/24/outline'
import { Squares2X2Icon, StopIcon, ArrowPathRoundedSquareIcon } from '@heroicons/react/20/solid'
import Script from "next/script";
import prisma from '@/app/lib/prisma';
import { unstable_cache } from 'next/cache';
import Cover from "@/app/components/cover";
import AdsBanner from '@/app/components/AdsBanner';

const itemPerPage = 24;


// Implement a more robust caching strategy with a 5-minute cache duration
const getBooks = unstable_cache(
  async (page: number, continued: boolean | undefined) => {
    if (page < 0) {
      page = 0;
    }

    // Fetch only necessary fields from elasticsearch
    const { total: totalNum, books } = await searchByTagAndComplete(page, itemPerPage, "", continued);
    const total = Math.ceil(Number(totalNum) / itemPerPage);

    return { books, total };
  },
  ['books-list'],
  { revalidate: 300 } // Cache for 5 minutes
);

type Params = {
  // params: { bookId: string }
  page: number | undefined
  continued: string | undefined
}

export default async function BookList({ searchParams }: {
  searchParams: Params;
}) {

  // console.log("page:", searchParams.page);
  const pageNum = searchParams.page ? Number(searchParams.page) : 0;
  let continued: boolean | undefined = undefined;
  if (searchParams.continued == "true") {
    continued = true;
  } else if (searchParams.continued == "false") {
    continued = false;
  } else {
    continued = undefined;
  }

  const tabs = [
    { name: '連載', href: '/books?continued=true', icon: ArrowPathRoundedSquareIcon, current: continued },
    { name: '完結', href: '/books?continued=false', icon: StopIcon, current: continued == false },
    { name: '全部', href: '/books?continued=', icon: Squares2X2Icon, current: continued == undefined },
  ]

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ')
  }

  const { books, total } = await getBooks(pageNum, continued);
  return (
    <div className='px-1'>
      <div className='flex items-center space-x-1 text-xl my-2 sm:my-4 text-foreground'>
        <Squares2X2IconOutline className="h-7 w-7 shrink-0" />
        <div>全部漫畫</div>
      </div>

      <div className="border-b border-gray-200">
        <nav aria-label="Tabs" className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <a
              key={tab.name}
              href={tab.href}
              aria-current={tab.current ? 'page' : undefined}
              className={classNames(
                tab.current
                  ? 'border-pink-500 text-pink-600'
                  : 'border-transparent text-gray-600 dark:text-gray-300 hover:border-gray-300 hover:text-gray-700',
                'group inline-flex items-center border-b-2 px-1 py-4 text-sm font-medium',
              )}
            >
              <tab.icon
                aria-hidden="true"
                className={classNames(
                  tab.current ? 'text-pink-500' : 'text-gray-400 dark:text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300',
                  '-ml-0.5 mr-2 h-5 w-5',
                )}
              />
              <span>{tab.name}</span>
            </a>
          ))}
        </nav>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 my-4">
        <div className='flex justify-center'>
          <AdsBanner id="ckunfc3nm01420hj9kqjfo2ea" />
        </div>
        <div className='flex justify-center'>
          <AdsBanner id="ckv6gba8c24350im3gr9wwg45" />
        </div>
      </div>
      <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
        {books.map((book: any, ind: number) => {
          const chapters = book.activeResource?.chapters;
          const lastChapter = chapters.at(-1);
          if (ind == 12) {
            return (
              <>
                <div className=' col-span-1 sm:col-span-4 md:col-span-6 grid grid-cols-2 gap-4 md:gap-8 my-2'>
                  <div className='flex justify-center'>
                    <AdsBanner id="cku5fxlu801090hmt8hzaoyt9" />
                  </div>
                  <div className='flex justify-center'>
                    <AdsBanner id="cku6pwjfx05060hn3o8bjbj6j" />
                  </div>
                </div>
                <ComicTile book={book} key={book.id} lastChapter={lastChapter} lastRead={null} />
              </>
            );
          } else {
            return (
              <ComicTile book={book} key={book.id} lastChapter={lastChapter} lastRead={null} />
            );
          }
        })}
      </div>

      <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-8 my-2 sm:my-6'>
        <div className='flex flex-col items-center space-y-2'>
          <ins className="eas6a97888e" data-zoneid="4454466"></ins>
          <ins id="__clb-1854687" />
        </div>
        <div className='flex justify-center'>
          <ins id="901192" data-width="300" data-height="250"></ins>
        </div>
      </div>

      <div className='my-8'>
        <Pagination path={`books?continued=${continued}`} current={pageNum} total={total} />
      </div>

      <div>
        <Cover url="https://ra12.xyz/z/clf7jci8h0001lt0i88nu0z2z/json" />
      </div>

      <Script src="https://a.magsrv.com/video-slider.js" />
      {pageNum > 1 && (
        <>
          <Script src="/js/vslider.js" />
        </>
      )}
      {/* Clickadu */}
      <Script
        data-cfasync="false"
        type="text/javascript"
        src="//chaseherbalpasty.com/lv/esnk/1854687/code.js"
        async
        id="__clb-1854687"
      />
    </div>
  )
}