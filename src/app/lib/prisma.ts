import { PrismaClient, Prisma } from "@prisma/client";

// Create singleton instances for both primary and replica
const prismaClientSingleton = () => {
    return new PrismaClient({
        datasources: {
            db: {
                url: process.env.DATABASE_PRIMARY_URL
            }
        }
    });
};

const prismaReplicaClientSingleton = () => {
    return new PrismaClient({
        datasources: {
            db: {
                url: process.env.DATABASE_REPLICA_URL
            }
        }
    });
};

declare global {
    var prisma: undefined | ReturnType<typeof prismaClientSingleton>;
    var prismaReplica: undefined | ReturnType<typeof prismaReplicaClientSingleton>;
}

const prisma = globalThis.prisma ?? prismaClientSingleton();
const prismaReplica = globalThis.prismaReplica ?? prismaReplicaClientSingleton();

if (process.env.NODE_ENV !== "production") {
    globalThis.prisma = prisma;
    globalThis.prismaReplica = prismaReplica;
}

// Add middleware to handle read replicas
prisma.$use(async (params, next) => {
    // List of read operations
    const readOperations = [
        'findUnique',
        'findFirst',
        'findMany',
        'count',
        'aggregate',
        'groupBy',
        'queryRaw',
        'findRaw'
    ];

    // Use read replica for read operations
    if (readOperations.includes(params.action)) {
        try {
            // Handle queryRaw separately as it's a direct client method
            if (params.action === 'queryRaw') {
                const [query, ...values] = params.args;
                return await prismaReplica.$queryRaw(Prisma.sql(query, ...values));
            }
            // Handle model-specific operations
            // @ts-ignore - params.model and action are always defined for valid queries
            return await prismaReplica[params.model][params.action](params.args);
        } catch (error: unknown) {
            console.warn('Read replica error, failing over to primary:', (error as Error).message);
            // Failover to primary database
            try {
                return next(params);
            } catch (primaryError: unknown) {
                console.error('Primary database error:', (primaryError as Error).message);
                throw primaryError;
            }
        }
    }

    return next(params);
});

export default prisma;
